package com.investment.model;

import com.investment.database.DatabaseManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Represents a portfolio position for a financial instrument.
 * This class tracks position details, risk management parameters, and performance metrics.
 */
public class Position {

    private static final Logger logger = LoggerFactory.getLogger(Position.class);

    /**
     * Position side enumeration.
     */
    public enum Side {
        BUY, SELL
    }
    
    /**
     * Position status enumeration.
     */
    public enum Status {
        OPEN, CLOSED
    }
    
    /**
     * Bollinger Band expansion/contraction enumeration.
     */
    public enum ExpandOrContract {
        EXPANDING, CONTRACTING
    }

    /**
     * Risk management mode enumeration.
     */
    public enum RiskMode {
        AGGRESSIVE, CONSERVATIVE
    }
    
    // Core position fields
    private Long id;
    private String symbol;
    private BigDecimal position;
    private Side side;
    private Status status;
    
    // Trade execution data
    private BigDecimal tradePrice;
    private BigDecimal tradeValue;
    private BigDecimal initPortfolioNetValue;
    
    // Current market data
    private BigDecimal lastPrice;
    private BigDecimal lastValue;
    
    // Risk management fields
    private BigDecimal riskUnit;
    private BigDecimal stopPercent;
    private BigDecimal highestAfterTrade;
    private BigDecimal stopValueFromHighest;

    // Enhanced risk management fields
    private LocalDateTime conservativePeriodEndDate;
    private RiskMode riskMode;
    
    // Bollinger Bands integration
    private BigDecimal lastBbmb;
    private BigDecimal bbmbAdjPercent;
    private BigDecimal stopValueFromBbmb;
    private ExpandOrContract expandOrContract;
    
    // Effective risk management
    private BigDecimal effectiveStopValue;
    
    // Performance metrics
    private BigDecimal pnlValue;
    private BigDecimal pnlPercent;

    // Close price (when position is closed)
    private BigDecimal closePrice;

    // Position dates
    private LocalDate openDate;
    private LocalDate closeDate;

    // Timestamps
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    
    // Default constructor
    public Position() {}
    
    // Constructor for creating new positions
    public Position(String symbol, BigDecimal position, Side side, BigDecimal tradePrice) {
        this.symbol = symbol;
        this.position = position;
        this.side = side;
        this.tradePrice = tradePrice;
        this.status = Status.OPEN;
        this.tradeValue = position.multiply(tradePrice);
        this.openDate = LocalDate.now(); // Set default open date to current date
        this.createdDate = LocalDateTime.now();
        this.updatedDate = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public BigDecimal getPosition() { return position; }
    public void setPosition(BigDecimal position) { this.position = position; }
    
    public Side getSide() { return side; }
    public void setSide(Side side) { this.side = side; }
    
    public Status getStatus() { return status; }
    public void setStatus(Status status) { this.status = status; }
    
    public BigDecimal getTradePrice() { return tradePrice; }
    public void setTradePrice(BigDecimal tradePrice) { this.tradePrice = tradePrice; }
    
    public BigDecimal getTradeValue() { return tradeValue; }
    public void setTradeValue(BigDecimal tradeValue) { this.tradeValue = tradeValue; }
    
    public BigDecimal getInitPortfolioNetValue() { return initPortfolioNetValue; }
    public void setInitPortfolioNetValue(BigDecimal initPortfolioNetValue) { this.initPortfolioNetValue = initPortfolioNetValue; }
    
    public BigDecimal getLastPrice() { return lastPrice; }
    public void setLastPrice(BigDecimal lastPrice) { this.lastPrice = lastPrice; }
    
    public BigDecimal getLastValue() { return lastValue; }
    public void setLastValue(BigDecimal lastValue) { this.lastValue = lastValue; }
    
    public BigDecimal getRiskUnit() { return riskUnit; }
    public void setRiskUnit(BigDecimal riskUnit) { this.riskUnit = riskUnit; }
    
    public BigDecimal getStopPercent() { return stopPercent; }
    public void setStopPercent(BigDecimal stopPercent) { this.stopPercent = stopPercent; }
    
    public BigDecimal getHighestAfterTrade() { return highestAfterTrade; }
    public void setHighestAfterTrade(BigDecimal highestAfterTrade) { this.highestAfterTrade = highestAfterTrade; }
    
    public BigDecimal getStopValueFromHighest() { return stopValueFromHighest; }
    public void setStopValueFromHighest(BigDecimal stopValueFromHighest) { this.stopValueFromHighest = stopValueFromHighest; }



    public LocalDateTime getConservativePeriodEndDate() { return conservativePeriodEndDate; }
    public void setConservativePeriodEndDate(LocalDateTime conservativePeriodEndDate) { this.conservativePeriodEndDate = conservativePeriodEndDate; }

    public RiskMode getRiskMode() { return riskMode; }
    public void setRiskMode(RiskMode riskMode) { this.riskMode = riskMode; }
    
    public BigDecimal getLastBbmb() { return lastBbmb; }
    public void setLastBbmb(BigDecimal lastBbmb) { this.lastBbmb = lastBbmb; }
    
    public BigDecimal getBbmbAdjPercent() { return bbmbAdjPercent; }
    public void setBbmbAdjPercent(BigDecimal bbmbAdjPercent) { this.bbmbAdjPercent = bbmbAdjPercent; }
    
    public BigDecimal getStopValueFromBbmb() { return stopValueFromBbmb; }
    public void setStopValueFromBbmb(BigDecimal stopValueFromBbmb) { this.stopValueFromBbmb = stopValueFromBbmb; }
    
    public ExpandOrContract getExpandOrContract() { return expandOrContract; }
    public void setExpandOrContract(ExpandOrContract expandOrContract) { this.expandOrContract = expandOrContract; }
    
    public BigDecimal getEffectiveStopValue() { return effectiveStopValue; }
    public void setEffectiveStopValue(BigDecimal effectiveStopValue) { this.effectiveStopValue = effectiveStopValue; }
    
    public BigDecimal getPnlValue() { return pnlValue; }
    public void setPnlValue(BigDecimal pnlValue) { this.pnlValue = pnlValue; }
    
    public BigDecimal getPnlPercent() { return pnlPercent; }
    public void setPnlPercent(BigDecimal pnlPercent) { this.pnlPercent = pnlPercent; }

    public BigDecimal getClosePrice() { return closePrice; }
    public void setClosePrice(BigDecimal closePrice) { this.closePrice = closePrice; }

    public LocalDate getOpenDate() { return openDate; }
    public void setOpenDate(LocalDate openDate) { this.openDate = openDate; }

    public LocalDate getCloseDate() { return closeDate; }
    public void setCloseDate(LocalDate closeDate) { this.closeDate = closeDate; }

    public LocalDateTime getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate = createdDate; }
    
    public LocalDateTime getUpdatedDate() { return updatedDate; }
    public void setUpdatedDate(LocalDateTime updatedDate) { this.updatedDate = updatedDate; }
    
    /**
     * Update the position with current market data and recalculate metrics.
     * This method uses the close price for P&L calculations and the same price for trailing stops.
     * For more accurate trailing stops, use updateMarketDataWithOHLCV() instead.
     */
    public void updateMarketData(BigDecimal currentPrice) {
        updateMarketDataWithPrices(currentPrice, currentPrice);
    }

    /**
     * Update the position with current market data using separate close and high prices.
     * This method provides more accurate trailing stop calculations by using the actual high price.
     *
     * @param closePrice The closing price for P&L calculations
     * @param highPrice The high price for trailing stop calculations
     */
    public void updateMarketDataWithPrices(BigDecimal closePrice, BigDecimal highPrice) {
        this.lastPrice = closePrice;
        this.lastValue = position.multiply(closePrice);

        // Calculate P&L using close price
        if (side == Side.BUY) {
            // For BUY positions: current value - initial cost
            this.pnlValue = lastValue.subtract(tradeValue);
        } else {
            // For SELL positions: initial proceeds - current cost to buy back
            // Since both tradeValue and lastValue are negative for short positions,
            // we need to negate both to get the actual cash flows
            this.pnlValue = tradeValue.negate().subtract(lastValue.negate());
        }

        // Calculate P&L percentage
        if (tradeValue.compareTo(BigDecimal.ZERO) != 0) {
            if (side == Side.BUY) {
                this.pnlPercent = pnlValue.divide(tradeValue, 6, BigDecimal.ROUND_HALF_UP);
            } else {
                // For SELL positions, use absolute value of tradeValue for percentage calculation
                this.pnlPercent = pnlValue.divide(tradeValue.abs(), 6, BigDecimal.ROUND_HALF_UP);
            }
        }

        // Update highest price for trailing stops using HIGH price (for BUY positions)
        if (side == Side.BUY && (highestAfterTrade == null || highPrice.compareTo(highestAfterTrade) > 0)) {
            this.highestAfterTrade = highPrice;
            logger.debug("Updated highestAfterTrade for BUY position to HIGH price: {}", highPrice);
        }

        // Update lowest price for trailing stops using LOW price (for SELL positions)
        // Note: For SELL positions, we should use the low price, not high price
        // But since this method only receives highPrice, we'll use closePrice as fallback
        if (side == Side.SELL && (highestAfterTrade == null || closePrice.compareTo(highestAfterTrade) < 0)) {
            this.highestAfterTrade = closePrice; // Using same field for lowest in SELL positions
            logger.debug("Updated highestAfterTrade for SELL position to close price: {}", closePrice);
        }

        // Recalculate stop values (will use enhanced logic if available)
        updateStopValues();

        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Update the position with complete OHLCV market data for most accurate calculations.
     * This method uses high price for BUY position trailing stops and low price for SELL position trailing stops.
     *
     * NOTE: This method still uses incremental price comparison for backward compatibility.
     * For SQL-based calculation using complete OHLCV history, use updateMarketDataWithOHLCVAndDatabase().
     *
     * @param closePrice The closing price for P&L calculations
     * @param highPrice The high price for BUY position trailing stops
     * @param lowPrice The low price for SELL position trailing stops
     */
    public void updateMarketDataWithOHLCV(BigDecimal closePrice, BigDecimal highPrice, BigDecimal lowPrice) {
        this.lastPrice = closePrice;
        this.lastValue = position.multiply(closePrice);

        // Calculate P&L using close price
        if (side == Side.BUY) {
            // For BUY positions: current value - initial cost
            this.pnlValue = lastValue.subtract(tradeValue);
        } else {
            // For SELL positions: initial proceeds - current cost to buy back
            // Since both tradeValue and lastValue are negative for short positions,
            // we need to negate both to get the actual cash flows
            this.pnlValue = tradeValue.negate().subtract(lastValue.negate());
        }

        // Calculate P&L percentage
        if (tradeValue.compareTo(BigDecimal.ZERO) != 0) {
            if (side == Side.BUY) {
                this.pnlPercent = pnlValue.divide(tradeValue, 6, BigDecimal.ROUND_HALF_UP);
            } else {
                // For SELL positions, use absolute value of tradeValue for percentage calculation
                this.pnlPercent = pnlValue.divide(tradeValue.abs(), 6, BigDecimal.ROUND_HALF_UP);
            }
        }

        // Update highest price for trailing stops using HIGH price (for BUY positions)
        if (side == Side.BUY && (highestAfterTrade == null || highPrice.compareTo(highestAfterTrade) > 0)) {
            this.highestAfterTrade = highPrice;
            logger.debug("Updated highestAfterTrade for BUY position to HIGH price: {}", highPrice);
        }

        // Update lowest price for trailing stops using LOW price (for SELL positions)
        if (side == Side.SELL && (highestAfterTrade == null || lowPrice.compareTo(highestAfterTrade) < 0)) {
            this.highestAfterTrade = lowPrice; // Using same field for lowest in SELL positions
            logger.debug("Updated highestAfterTrade for SELL position to LOW price: {}", lowPrice);
        }

        // Recalculate stop values (will use enhanced logic if available)
        updateStopValues();

        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Update the position with complete OHLCV market data using SQL-based calculation for most accurate results.
     * This method calculates highestAfterTrade using the complete OHLCV history from the database.
     *
     * @param closePrice The closing price for P&L calculations
     * @param highPrice The high price for current period (used for logging/validation)
     * @param lowPrice The low price for current period (used for logging/validation)
     * @param databaseManager The database manager to query OHLCV data
     */
    public void updateMarketDataWithOHLCVAndDatabase(BigDecimal closePrice, BigDecimal highPrice, BigDecimal lowPrice, DatabaseManager databaseManager) {
        // Update basic market data and P&L
        this.lastPrice = closePrice;
        this.lastValue = position.multiply(closePrice);

        // Calculate P&L using close price
        if (side == Side.BUY) {
            // For BUY positions: current value - initial cost
            this.pnlValue = lastValue.subtract(tradeValue);
        } else {
            // For SELL positions: initial proceeds - current cost to buy back
            // Since both tradeValue and lastValue are negative for short positions,
            // we need to negate both to get the actual cash flows
            this.pnlValue = tradeValue.negate().subtract(lastValue.negate());
        }

        // Calculate P&L percentage
        if (tradeValue.compareTo(BigDecimal.ZERO) != 0) {
            if (side == Side.BUY) {
                this.pnlPercent = pnlValue.divide(tradeValue, 6, BigDecimal.ROUND_HALF_UP);
            } else {
                // For SELL positions, use absolute value of tradeValue for percentage calculation
                this.pnlPercent = pnlValue.divide(tradeValue.abs(), 6, BigDecimal.ROUND_HALF_UP);
            }
        }

        // Calculate highestAfterTrade using SQL-based OHLCV data for maximum accuracy
        boolean calculationSuccess = calculateHighestAfterTradeFromOHLCV(databaseManager);

        if (!calculationSuccess) {
            // Fallback to incremental calculation if SQL-based calculation fails
            logger.warn("SQL-based highestAfterTrade calculation failed for {} position {}, falling back to incremental calculation",
                       side, symbol);

            if (side == Side.BUY && (highestAfterTrade == null || highPrice.compareTo(highestAfterTrade) > 0)) {
                this.highestAfterTrade = highPrice;
                logger.debug("Fallback: Updated highestAfterTrade for BUY position to HIGH price: {}", highPrice);
            } else if (side == Side.SELL && (highestAfterTrade == null || lowPrice.compareTo(highestAfterTrade) < 0)) {
                this.highestAfterTrade = lowPrice;
                logger.debug("Fallback: Updated highestAfterTrade for SELL position to LOW price: {}", lowPrice);
            }
        }

        // Calculate / retrieve lastBbmb from database
        queryLastBbmbFromDatabase(databaseManager);

        // Recalculate stop values (will use enhanced logic if available)
        updateStopValues();

        this.updatedDate = LocalDateTime.now();
    }
    
    /**
     * Update stop loss values based on current parameters.
     * This method maintains backward compatibility while supporting enhanced risk management.
     */
    public void updateStopValues() {
        logger.debug("updateStopValues called without riskManagementService");
        updateStopValues(null);
    }

    /**
     * Update stop loss values with optional enhanced risk management service.
     *
     * @param riskManagementService Optional service for enhanced risk calculations
     */
    public void updateStopValues(Object riskManagementService) {
        // Calculate stop from highest/lowest using current risk mode
        calculateStopFromHighest();

        // Calculate stop from Bollinger Band Middle Band
        calculateStopFromBollingerBand();

        // Try enhanced risk management if service is available and parameters are set
        BigDecimal enhancedStopValue = null;
        //if (riskManagementService != null && hasEnhancedRiskParameters()) {
        if (riskManagementService != null) {
            try {
                // Use reflection to call the service method to avoid circular dependency
                java.lang.reflect.Method method = riskManagementService.getClass()
                    .getMethod("calculateEnhancedEffectiveStopValue", Position.class);
                enhancedStopValue = (BigDecimal) method.invoke(riskManagementService, this);
            } catch (Exception e) {
                // Log error but continue with standard calculation
                System.err.println("Enhanced risk calculation failed: " + e.getMessage());
            }
        }

        // Determine effective stop value
        if (enhancedStopValue != null) {
            // Use enhanced calculation result
            this.effectiveStopValue = enhancedStopValue;
        } else {
            // Fall back to standard logic
            calculateStandardEffectiveStopValue();
        }
    }

    /**
     * Calculate stop value from highest/lowest price using appropriate risk mode.
     */
    private void calculateStopFromHighest() {
        if (highestAfterTrade == null) {
            logger.warn("Cannot calculate stopValueFromHighest: highestAfterTrade is null");
            return;
        }

        BigDecimal currentStopPercent = getCurrentStopPercent();
        if (currentStopPercent == null) {
            logger.warn("Cannot calculate stopValueFromHighest: stopPercent is null");
            return;
        }

        if (side == Side.BUY) {
            this.stopValueFromHighest = highestAfterTrade.multiply(BigDecimal.ONE.subtract(currentStopPercent));
            logger.debug("side=BUY highestAfterTrade={}, stopValueFromHighest={}", highestAfterTrade, stopValueFromHighest);
        } else {
            this.stopValueFromHighest = highestAfterTrade.multiply(BigDecimal.ONE.add(currentStopPercent));
            logger.debug("side=SELL highestAfterTrade={}, stopValueFromHighest={}", highestAfterTrade, stopValueFromHighest);
        }
    }

    /**
     * Get the current stop percentage.
     * All stop loss modes (ENHANCED and STANDARD) and risk modes (CONSERVATIVE and AGGRESSIVE)
     * use the same stop_percentage from the positions table.
     */
    private BigDecimal getCurrentStopPercent() {
        return stopPercent;
    }

    /**
     * Calculate stop value from Bollinger Band Middle Band.
     */
    private void calculateStopFromBollingerBand() {
        if (lastBbmb != null && bbmbAdjPercent != null) {
            if (side == Side.BUY) {
                this.stopValueFromBbmb = lastBbmb.multiply(BigDecimal.ONE.subtract(bbmbAdjPercent));
                logger.debug("lastBbmb={}, bbmbAdjPercent={}, stopValueFromBbmb={}", lastBbmb, bbmbAdjPercent, stopValueFromBbmb);
            } else {
                this.stopValueFromBbmb = lastBbmb.multiply(BigDecimal.ONE.add(bbmbAdjPercent));
            }
        }
        logger.debug("lastBbmb={}, bbmbAdjPercent={} , stopValueFromBbmb={}", lastBbmb, bbmbAdjPercent, stopValueFromBbmb);
    }

    /**
     * Calculate effective stop value using standard logic.
     */
    private void calculateStandardEffectiveStopValue() {
        if (stopValueFromHighest != null && stopValueFromBbmb != null) {
            if (side == Side.BUY) {
                // For BUY positions, use the higher stop value (more conservative)
                this.effectiveStopValue = stopValueFromHighest.max(stopValueFromBbmb);
            } else {
                // For SELL positions, use the lower stop value (more conservative)
                this.effectiveStopValue = stopValueFromHighest.min(stopValueFromBbmb);
            }
        } else if (stopValueFromHighest != null) {
            this.effectiveStopValue = stopValueFromHighest;
        } else if (stopValueFromBbmb != null) {
            this.effectiveStopValue = stopValueFromBbmb;
        }
    }


    /**
     * Check if the position should be stopped out based on current price.
     */
    public boolean shouldStopOut(BigDecimal currentPrice) {
        if (effectiveStopValue == null || status != Status.OPEN) {
            return false;
        }
        
        if (side == Side.BUY) {
            return currentPrice.compareTo(effectiveStopValue) <= 0;
        } else {
            return currentPrice.compareTo(effectiveStopValue) >= 0;
        }
    }
    
    /**
     * Close the position.
     */
    public void close() {
        this.status = Status.CLOSED;
        this.closeDate = LocalDate.now(); // Set close date to current date
        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Close the position with a specific close price and calculate final P&L.
     */
    public void close(BigDecimal closePrice) {
        this.status = Status.CLOSED;
        this.closePrice = closePrice;
        this.closeDate = LocalDate.now(); // Set close date to current date

        // Calculate final P&L based on close price
        calculatePnLWithClosePrice(closePrice);

        this.updatedDate = LocalDateTime.now();
    }

    /**
     * Reopen a closed position, setting its status back to OPEN.
     * This clears the close date and allows the position to be actively managed again.
     */
    public void reopen() {
        if (this.status != Status.CLOSED) {
            throw new IllegalStateException("Only CLOSED positions can be reopened. Current status: " + this.status);
        }

        this.status = Status.OPEN;
        this.closeDate = null; // Clear close date when reopening
        this.updatedDate = LocalDateTime.now();

        logger.info("Reopened position for symbol: {}", this.symbol);
    }

    /**
     * Calculate P&L using a specific price (typically close price).
     * This method follows the same logic as updateMarketData but uses the provided price.
     */
    public void calculatePnLWithClosePrice(BigDecimal price) {
        if (price == null || tradePrice == null || position == null) {
            return;
        }

        BigDecimal priceValue = position.multiply(price);

        // Calculate P&L
        if (side == Side.BUY) {
            // For BUY positions: current value - initial cost
            this.pnlValue = priceValue.subtract(tradeValue);
        } else {
            // For SELL positions: initial proceeds - current cost to buy back
            // Since both tradeValue and priceValue are negative for short positions,
            // we need to negate both to get the actual cash flows
            this.pnlValue = tradeValue.negate().subtract(priceValue.negate());
        }

        // Calculate P&L percentage
        if (tradeValue != null && tradeValue.compareTo(BigDecimal.ZERO) != 0) {
            if (side == Side.BUY) {
                this.pnlPercent = pnlValue.divide(tradeValue, 6, BigDecimal.ROUND_HALF_UP);
            } else {
                // For SELL positions, use absolute value of tradeValue for percentage calculation
                this.pnlPercent = pnlValue.divide(tradeValue.abs(), 6, BigDecimal.ROUND_HALF_UP);
            }
        }
    }

    /**
     * Calculate P&L based on position status.
     * For OPEN positions: uses the provided current market price
     * For CLOSED positions: uses the position's own close_price field
     *
     * @param currentMarketPrice The current market price (used only for OPEN positions)
     */
    public void calculatePnLBasedOnStatus(BigDecimal currentMarketPrice) {
        if (status == Status.CLOSED) {
            // For CLOSED positions, use the position's close_price
            if (closePrice != null) {
                calculatePnLWithClosePrice(closePrice);
            }
            // If closePrice is null for a CLOSED position, P&L remains unchanged
        } else {
            // For OPEN positions, use current market price
            if (currentMarketPrice != null) {
                updateMarketData(currentMarketPrice);
            }
        }
    }

    /**
     * Check if this position should use market price for P&L calculations.
     * CLOSED positions should not be updated with market prices.
     *
     * @return true if position should use market price, false otherwise
     */
    public boolean shouldUseMarketPrice() {
        return status == Status.OPEN;
    }

    /**
     * Calculate and update the highestAfterTrade value using SQL-based OHLCV data.
     * This method queries the OHLCV database to get the actual maximum high (for BUY positions)
     * or minimum low (for SELL positions) since the position was opened.
     *
     * @param databaseManager The database manager to query OHLCV data
     * @return true if the calculation was successful, false otherwise
     */
    public boolean calculateHighestAfterTradeFromOHLCV(DatabaseManager databaseManager) {
        if (symbol == null || openDate == null || databaseManager == null) {
            logger.warn("Cannot calculate highestAfterTrade: missing symbol, openDate, or databaseManager");
            return false;
        }

        try {
            // Determine the date range for OHLCV query
            LocalDate startDate = openDate;
            LocalDate endDate = (status == Status.CLOSED && closeDate != null) ? closeDate : null;

            BigDecimal calculatedValue = null;

            if (side == Side.BUY) {
                // For BUY positions: get maximum high value from OHLCV data
                calculatedValue = databaseManager.getMaxHighPrice(symbol, startDate, endDate);
                if (calculatedValue != null) {
                    logger.debug("Calculated highestAfterTrade for BUY position {} from OHLCV MAX(high): {} (date range: {} to {})",
                               symbol, calculatedValue, startDate, endDate);
                }
            } else if (side == Side.SELL) {
                // For SELL positions: get minimum low value from OHLCV data
                calculatedValue = databaseManager.getMinLowPrice(symbol, startDate, endDate);
                if (calculatedValue != null) {
                    logger.debug("Calculated highestAfterTrade for SELL position {} from OHLCV MIN(low): {} (date range: {} to {})",
                               symbol, calculatedValue, startDate, endDate);
                }
            }

            if (calculatedValue != null) {
                // Update the highestAfterTrade value
                this.highestAfterTrade = calculatedValue;
                this.updatedDate = LocalDateTime.now();

                logger.info("Updated highestAfterTrade for {} position {}: {} (calculated from OHLCV data)",
                           side, symbol, calculatedValue);
                return true;
            } else {
                logger.warn("No OHLCV data found for {} position {} in date range {} to {}",
                           side, symbol, startDate, endDate);
                return false;
            }

        } catch (Exception e) {
            logger.error("Error calculating highestAfterTrade from OHLCV data for {} position {}: {}",
                        side, symbol, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Query and update the lastBbmb value from the database using Bollinger Band data.
     * For OPEN positions: gets the most recent bb_middle_band value.
     * For CLOSED positions: gets the bb_middle_band value for the position's close date.
     *
     * @param databaseManager The database manager to query Bollinger Band data
     */
    public void queryLastBbmbFromDatabase(DatabaseManager databaseManager) {
        if (symbol == null || databaseManager == null) {
            logger.warn("Cannot query last BBMB: missing symbol or databaseManager");
            return;
        }

        try {
            BigDecimal bbmbValue;

            if (status == Status.CLOSED && closeDate != null) {
                // For CLOSED positions: get bb_middle_band value for the close date
                bbmbValue = databaseManager.getBollingerBandMiddleBand(symbol, closeDate);
                if (bbmbValue != null) {
                    logger.debug("Retrieved BBMB for CLOSED position {} on close date {}: {}",
                               symbol, closeDate, bbmbValue);
                } else {
                    logger.warn("No Bollinger Band data found for CLOSED position {} on close date {}",
                               symbol, closeDate);
                }
            } else {
                // For OPEN positions: get most recent bb_middle_band value
                bbmbValue = databaseManager.getBollingerBandMiddleBand(symbol, null);
                if (bbmbValue != null) {
                    logger.debug("Retrieved latest BBMB for OPEN position {}: {}", symbol, bbmbValue);
                } else {
                    logger.warn("No recent Bollinger Band data found for OPEN position {}", symbol);
                }
            }

            if (bbmbValue != null) {
                // Update the lastBbmb field
                this.lastBbmb = bbmbValue;
                this.updatedDate = LocalDateTime.now();

                logger.info("Updated lastBbmb for {} position {}: {} (queried from database)",
                           status, symbol, bbmbValue);
            } else {
                logger.warn("Failed to retrieve Bollinger Band data for {} position {} - lastBbmb remains unchanged",
                           status, symbol);
            }

        } catch (Exception e) {
            logger.error("Error querying lastBbmb from database for {} position {}: {}",
                        status, symbol, e.getMessage(), e);
        }
    }
    
    @Override
    public String toString() {
        return String.format("Position{id=%d, symbol='%s', position=%s, side=%s, status=%s, tradePrice=%s, lastPrice=%s, pnlValue=%s, pnlPercent=%s, openDate=%s, close=%s}",
                id, symbol, position, side, status, tradePrice, lastPrice, pnlValue, pnlPercent, openDate, closeDate);
    }
}
